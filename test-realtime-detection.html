<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Detecção em Tempo Real - WhatsApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .test-button {
            background: #25d366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #128c7e;
        }
        
        .result {
            background: #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            min-height: 60px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            border-left: 4px solid #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }
        
        .error {
            border-left: 4px solid #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }
        
        .warning {
            border-left: 4px solid #f39c12;
            background: rgba(243, 156, 18, 0.1);
        }
        
        select, input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #333;
            border: 1px solid #555;
            border-radius: 5px;
            color: #fff;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .log-entry {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 0.9em;
        }
        
        .log-info {
            background: rgba(52, 152, 219, 0.2);
            border-left: 3px solid #3498db;
        }
        
        .log-success {
            background: rgba(39, 174, 96, 0.2);
            border-left: 3px solid #27ae60;
        }
        
        .log-error {
            background: rgba(231, 76, 60, 0.2);
            border-left: 3px solid #e74c3c;
        }
        
        .clear-log {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
        }
        
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🧪 Teste de Detecção em Tempo Real - WhatsApp</h1>
    <p>Este teste verifica se a detecção do ícone do WhatsApp funciona imediatamente ao selecionar no dropdown.</p>
    
    <div class="test-grid">
        <div class="test-section">
            <h2>📱 Simulação do Modal "Adicionar Link"</h2>
            <div class="form-group">
                <label for="add-icon-select">Ícone do Serviço:</label>
                <select id="add-icon-select">
                    <option value="">Selecione um ícone</option>
                    <option value="fab fa-whatsapp">📱 WhatsApp</option>
                    <option value="fab fa-instagram">📷 Instagram</option>
                    <option value="fab fa-facebook">📘 Facebook</option>
                    <option value="fab fa-twitter">🐦 Twitter</option>
                    <option value="fas fa-globe">🌐 Site/Link</option>
                    <option value="fas fa-envelope">✉️ Email</option>
                </select>
            </div>
            
            <div id="add-form-fields">
                <!-- Campos serão inseridos aqui dinamicamente -->
            </div>
            
            <button class="test-button" onclick="testAddFormDetection()">Testar Detecção</button>
        </div>
        
        <div class="test-section">
            <h2>✏️ Simulação do Modal "Editar Link"</h2>
            <div class="form-group">
                <label for="edit-icon-select">Ícone do Serviço:</label>
                <select id="edit-icon-select">
                    <option value="">Selecione um ícone</option>
                    <option value="fab fa-whatsapp">📱 WhatsApp</option>
                    <option value="fab fa-instagram">📷 Instagram</option>
                    <option value="fab fa-facebook">📘 Facebook</option>
                    <option value="fab fa-twitter">🐦 Twitter</option>
                    <option value="fas fa-globe">🌐 Site/Link</option>
                    <option value="fas fa-envelope">✉️ Email</option>
                </select>
            </div>
            
            <div id="edit-form-fields">
                <!-- Campos serão inseridos aqui dinamicamente -->
            </div>
            
            <button class="test-button" onclick="testEditFormDetection()">Testar Detecção</button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 Log de Eventos em Tempo Real</h2>
        <button class="clear-log" onclick="clearLog()">Limpar Log</button>
        <div class="result" id="event-log">
            <div class="log-info">Aguardando eventos...</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Teste Automático de Sequência</h2>
        <p>Este teste simula a sequência completa de seleção de ícones para verificar a detecção.</p>
        <button class="test-button" onclick="runAutomaticTest()">Executar Teste Automático</button>
        <div class="result" id="automatic-test-result">
            Clique em "Executar Teste Automático" para iniciar
        </div>
    </div>

    <script>
        let eventCount = 0;
        
        // Simula as funções do ConfigManager
        function detectLinkType(iconClass) {
            const linkTypeMap = {
                'fab fa-whatsapp': 'whatsapp',
                'fab fa-telegram': 'telegram',
                'fas fa-envelope': 'email',
                'fas fa-phone': 'phone'
            };
            
            return linkTypeMap[iconClass] || 'default';
        }
        
        function logEvent(message, type = 'info') {
            eventCount++;
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] #${eventCount}: ${message}`;
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('event-log').innerHTML = '<div class="log-info">Log limpo - aguardando eventos...</div>';
            eventCount = 0;
        }
        
        function updateFormFields(formType, iconClass) {
            const linkType = detectLinkType(iconClass);
            const fieldsContainer = document.getElementById(`${formType}-form-fields`);
            
            logEvent(`Atualizando campos para ${formType}: ${iconClass} → ${linkType}`, 'info');
            
            if (linkType === 'whatsapp') {
                fieldsContainer.innerHTML = `
                    <div style="background: rgba(37, 211, 102, 0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4 style="color: #25d366; margin: 0 0 10px 0;">📱 Campos Específicos do WhatsApp</h4>
                        <div class="form-group">
                            <label>Número do WhatsApp *</label>
                            <input type="tel" placeholder="5575991929294" style="border: 2px solid #25d366;">
                        </div>
                        <div class="form-group">
                            <label>Mensagem (opcional)</label>
                            <textarea placeholder="Olá! Gostaria de agendar..." rows="3" style="border: 2px solid #25d366;"></textarea>
                        </div>
                    </div>
                `;
                logEvent(`✅ Campos específicos do WhatsApp criados para ${formType}`, 'success');
            } else {
                fieldsContainer.innerHTML = `
                    <div style="background: rgba(108, 92, 231, 0.1); padding: 15px; border-radius: 8px; margin: 10px 0;">
                        <h4 style="color: #6c5ce7; margin: 0 0 10px 0;">🔗 Campos Padrão de URL</h4>
                        <div class="form-group">
                            <label>Protocolo</label>
                            <select style="border: 2px solid #6c5ce7;">
                                <option>https://</option>
                                <option>http://</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>URL *</label>
                            <input type="text" placeholder="www.exemplo.com" style="border: 2px solid #6c5ce7;">
                        </div>
                    </div>
                `;
                logEvent(`✅ Campos padrão de URL criados para ${formType}`, 'success');
            }
        }
        
        function setupEventListeners() {
            // Listener para formulário de adição
            const addSelect = document.getElementById('add-icon-select');
            addSelect.addEventListener('change', (event) => {
                const iconClass = event.target.value;
                logEvent(`🎯 ADD FORM: Ícone selecionado → ${iconClass}`, 'info');
                
                if (iconClass) {
                    updateFormFields('add', iconClass);
                } else {
                    document.getElementById('add-form-fields').innerHTML = '';
                    logEvent(`🧹 ADD FORM: Campos limpos`, 'info');
                }
            });
            
            // Listener para formulário de edição
            const editSelect = document.getElementById('edit-icon-select');
            editSelect.addEventListener('change', (event) => {
                const iconClass = event.target.value;
                logEvent(`🎯 EDIT FORM: Ícone selecionado → ${iconClass}`, 'info');
                
                if (iconClass) {
                    updateFormFields('edit', iconClass);
                } else {
                    document.getElementById('edit-form-fields').innerHTML = '';
                    logEvent(`🧹 EDIT FORM: Campos limpos`, 'info');
                }
            });
            
            logEvent('🔧 Event listeners configurados', 'success');
        }
        
        function testAddFormDetection() {
            const select = document.getElementById('add-icon-select');
            const selectedValue = select.value;
            
            if (!selectedValue) {
                logEvent('❌ Nenhum ícone selecionado no formulário de adição', 'error');
                return;
            }
            
            const linkType = detectLinkType(selectedValue);
            logEvent(`🧪 TESTE ADD: ${selectedValue} → ${linkType}`, 'info');
            
            if (linkType === 'whatsapp') {
                const whatsappFields = document.querySelector('#add-form-fields [style*="25d366"]');
                if (whatsappFields) {
                    logEvent('✅ TESTE ADD: Campos do WhatsApp detectados corretamente!', 'success');
                } else {
                    logEvent('❌ TESTE ADD: Campos do WhatsApp NÃO foram criados!', 'error');
                }
            } else {
                const defaultFields = document.querySelector('#add-form-fields [style*="6c5ce7"]');
                if (defaultFields) {
                    logEvent('✅ TESTE ADD: Campos padrão detectados corretamente!', 'success');
                } else {
                    logEvent('❌ TESTE ADD: Campos padrão NÃO foram criados!', 'error');
                }
            }
        }
        
        function testEditFormDetection() {
            const select = document.getElementById('edit-icon-select');
            const selectedValue = select.value;
            
            if (!selectedValue) {
                logEvent('❌ Nenhum ícone selecionado no formulário de edição', 'error');
                return;
            }
            
            const linkType = detectLinkType(selectedValue);
            logEvent(`🧪 TESTE EDIT: ${selectedValue} → ${linkType}`, 'info');
            
            if (linkType === 'whatsapp') {
                const whatsappFields = document.querySelector('#edit-form-fields [style*="25d366"]');
                if (whatsappFields) {
                    logEvent('✅ TESTE EDIT: Campos do WhatsApp detectados corretamente!', 'success');
                } else {
                    logEvent('❌ TESTE EDIT: Campos do WhatsApp NÃO foram criados!', 'error');
                }
            } else {
                const defaultFields = document.querySelector('#edit-form-fields [style*="6c5ce7"]');
                if (defaultFields) {
                    logEvent('✅ TESTE EDIT: Campos padrão detectados corretamente!', 'success');
                } else {
                    logEvent('❌ TESTE EDIT: Campos padrão NÃO foram criados!', 'error');
                }
            }
        }
        
        function runAutomaticTest() {
            const resultDiv = document.getElementById('automatic-test-result');
            resultDiv.innerHTML = '🔄 Executando teste automático...';
            
            logEvent('🚀 Iniciando teste automático de sequência', 'info');
            
            const testSequence = [
                { form: 'add', icon: 'fab fa-whatsapp', expected: 'whatsapp' },
                { form: 'add', icon: 'fab fa-instagram', expected: 'default' },
                { form: 'edit', icon: 'fab fa-whatsapp', expected: 'whatsapp' },
                { form: 'edit', icon: 'fas fa-globe', expected: 'default' }
            ];
            
            let passed = 0;
            let total = testSequence.length;
            
            testSequence.forEach((test, index) => {
                setTimeout(() => {
                    const select = document.getElementById(`${test.form}-icon-select`);
                    select.value = test.icon;
                    
                    // Dispara evento change
                    const event = new Event('change', { bubbles: true });
                    select.dispatchEvent(event);
                    
                    // Verifica resultado
                    setTimeout(() => {
                        const linkType = detectLinkType(test.icon);
                        const isCorrect = linkType === test.expected;
                        
                        if (isCorrect) {
                            passed++;
                            logEvent(`✅ Teste ${index + 1}/${total}: ${test.form} + ${test.icon} = ${linkType} ✓`, 'success');
                        } else {
                            logEvent(`❌ Teste ${index + 1}/${total}: ${test.form} + ${test.icon} = ${linkType} (esperado: ${test.expected})`, 'error');
                        }
                        
                        // Se é o último teste, mostra resultado final
                        if (index === total - 1) {
                            const percentage = Math.round((passed / total) * 100);
                            const finalResult = `
                                📊 <strong>Resultado Final:</strong><br>
                                ✅ Testes aprovados: ${passed}/${total} (${percentage}%)<br>
                                ${percentage === 100 ? '🎉 <strong>TODOS OS TESTES PASSARAM!</strong>' : '⚠️ <strong>Alguns testes falharam</strong>'}
                            `;
                            
                            resultDiv.innerHTML = finalResult;
                            resultDiv.className = percentage === 100 ? 'result success' : 'result warning';
                            
                            logEvent(`🏁 Teste automático concluído: ${passed}/${total} (${percentage}%)`, percentage === 100 ? 'success' : 'error');
                        }
                    }, 100);
                }, index * 1000);
            });
        }
        
        // Inicializa os event listeners quando a página carrega
        document.addEventListener('DOMContentLoaded', () => {
            setupEventListeners();
            logEvent('🔧 Página carregada e listeners inicializados', 'success');
        });
    </script>
</body>
</html>
