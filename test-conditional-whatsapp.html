<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Condicional WhatsApp - Link Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .test-button {
            background: #25d366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #128c7e;
        }
        
        .test-button.secondary {
            background: #6c5ce7;
        }
        
        .test-button.secondary:hover {
            background: #5a4fcf;
        }
        
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            min-height: 50px;
        }
        
        .success {
            border-left: 4px solid #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }
        
        .error {
            border-left: 4px solid #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }
        
        .warning {
            border-left: 4px solid #f39c12;
            background: rgba(243, 156, 18, 0.1);
        }
        
        select, input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #333;
            border: 1px solid #555;
            border-radius: 5px;
            color: #fff;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🧪 Teste Condicional da Funcionalidade WhatsApp</h1>
    <p>Este teste verifica se os campos específicos do WhatsApp aparecem APENAS quando o ícone do WhatsApp é selecionado.</p>
    
    <div class="test-grid">
        <div class="test-section">
            <h2>📱 Teste de Detecção Condicional</h2>
            <div class="form-group">
                <label for="test-icon-select">Selecione um ícone:</label>
                <select id="test-icon-select">
                    <option value="">Selecione um ícone</option>
                    <option value="fab fa-whatsapp">📱 WhatsApp</option>
                    <option value="fab fa-instagram">📷 Instagram</option>
                    <option value="fab fa-facebook">📘 Facebook</option>
                    <option value="fab fa-twitter">🐦 Twitter</option>
                    <option value="fas fa-globe">🌐 Site/Link</option>
                    <option value="fas fa-envelope">✉️ Email</option>
                </select>
            </div>
            
            <button class="test-button" onclick="testIconDetection()">Testar Detecção</button>
            
            <div class="result" id="detection-result">
                Selecione um ícone e clique em "Testar Detecção"
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔄 Teste de Transição de Campos</h2>
            <div class="form-group">
                <label>Simular mudança de ícone:</label>
                <button class="test-button" onclick="simulateIconChange('fab fa-whatsapp')">→ WhatsApp</button>
                <button class="test-button secondary" onclick="simulateIconChange('fab fa-instagram')">→ Instagram</button>
                <button class="test-button secondary" onclick="simulateIconChange('fas fa-globe')">→ Site</button>
            </div>
            
            <div class="result" id="transition-result">
                Clique em um dos botões acima para simular mudança de ícone
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>✅ Teste de Validação Condicional</h2>
        <div class="form-group">
            <label for="test-validation-icon">Ícone para validação:</label>
            <select id="test-validation-icon">
                <option value="fab fa-whatsapp">📱 WhatsApp</option>
                <option value="fab fa-instagram">📷 Instagram</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="test-number">Número (apenas para WhatsApp):</label>
            <input type="tel" id="test-number" placeholder="5575991929294" value="5575991929294">
        </div>
        
        <button class="test-button" onclick="testConditionalValidation()">Testar Validação Condicional</button>
        
        <div class="result" id="validation-result">
            Configure o ícone e clique em "Testar Validação Condicional"
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧹 Teste de Limpeza de Campos</h2>
        <p>Este teste verifica se os campos específicos do WhatsApp são limpos ao trocar para outro ícone.</p>
        
        <div class="form-group">
            <label>Sequência de teste:</label>
            <button class="test-button" onclick="testFieldCleanup()">Executar Teste Completo</button>
        </div>
        
        <div class="result" id="cleanup-result">
            Clique em "Executar Teste Completo" para verificar a limpeza de campos
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 Resultados dos Testes</h2>
        <div class="result" id="overall-results">
            Execute os testes acima para ver os resultados consolidados
        </div>
    </div>

    <script>
        let testResults = {
            detection: false,
            transition: false,
            validation: false,
            cleanup: false
        };

        // Simula as funções do ConfigManager
        function detectLinkType(iconClass) {
            const linkTypeMap = {
                'fab fa-whatsapp': 'whatsapp',
                'fab fa-telegram': 'telegram',
                'fas fa-envelope': 'email',
                'fas fa-phone': 'phone'
            };
            
            return linkTypeMap[iconClass] || 'default';
        }

        function testIconDetection() {
            const iconSelect = document.getElementById('test-icon-select');
            const selectedIcon = iconSelect.value;
            const resultDiv = document.getElementById('detection-result');
            
            if (!selectedIcon) {
                resultDiv.innerHTML = '<span style="color: #f39c12;">⚠️ Nenhum ícone selecionado</span>';
                resultDiv.className = 'result warning';
                return;
            }
            
            const detectedType = detectLinkType(selectedIcon);
            const isWhatsApp = detectedType === 'whatsapp';
            
            let result = `
                🎯 <strong>Ícone selecionado:</strong> ${selectedIcon}<br>
                🔍 <strong>Tipo detectado:</strong> ${detectedType}<br>
                📱 <strong>É WhatsApp?</strong> ${isWhatsApp ? 'SIM' : 'NÃO'}<br>
                ✅ <strong>Campos específicos devem aparecer?</strong> ${isWhatsApp ? 'SIM' : 'NÃO'}
            `;
            
            if (isWhatsApp) {
                result += '<br><br>✅ <strong>CORRETO:</strong> Campos específicos do WhatsApp devem ser exibidos';
                resultDiv.className = 'result success';
                testResults.detection = true;
            } else {
                result += '<br><br>✅ <strong>CORRETO:</strong> Apenas campos padrão de URL devem ser exibidos';
                resultDiv.className = 'result success';
                testResults.detection = true;
            }
            
            resultDiv.innerHTML = result;
            updateOverallResults();
        }

        function simulateIconChange(iconClass) {
            const resultDiv = document.getElementById('transition-result');
            const detectedType = detectLinkType(iconClass);
            const isWhatsApp = detectedType === 'whatsapp';
            
            let result = `
                🔄 <strong>Simulando mudança para:</strong> ${iconClass}<br>
                🎯 <strong>Tipo detectado:</strong> ${detectedType}<br>
            `;
            
            if (isWhatsApp) {
                result += `
                    📱 <strong>Ação esperada:</strong><br>
                    • Esconder campos padrão de URL<br>
                    • Mostrar campos específicos do WhatsApp<br>
                    • Aplicar animação de transição (200-300ms)<br>
                    • Configurar validação específica
                `;
                resultDiv.className = 'result success';
            } else {
                result += `
                    🔗 <strong>Ação esperada:</strong><br>
                    • Remover campos específicos do WhatsApp<br>
                    • Mostrar campos padrão de URL<br>
                    • Limpar valores dos campos do WhatsApp<br>
                    • Aplicar animação de transição (200-300ms)
                `;
                resultDiv.className = 'result success';
            }
            
            resultDiv.innerHTML = result;
            testResults.transition = true;
            updateOverallResults();
        }

        function testConditionalValidation() {
            const iconSelect = document.getElementById('test-validation-icon');
            const numberInput = document.getElementById('test-number');
            const resultDiv = document.getElementById('validation-result');
            
            const selectedIcon = iconSelect.value;
            const number = numberInput.value;
            const detectedType = detectLinkType(selectedIcon);
            const isWhatsApp = detectedType === 'whatsapp';
            
            let result = `
                🎯 <strong>Ícone:</strong> ${selectedIcon}<br>
                📞 <strong>Número:</strong> ${number}<br>
                🔍 <strong>Tipo:</strong> ${detectedType}<br>
            `;
            
            if (isWhatsApp) {
                const isValidNumber = number.length >= 10 && number.length <= 15 && /^\d+$/.test(number);
                result += `
                    📱 <strong>Validação WhatsApp aplicada:</strong><br>
                    • Número válido: ${isValidNumber ? '✅ SIM' : '❌ NÃO'}<br>
                    • Campos específicos visíveis: ✅ SIM<br>
                    • Validação condicional: ✅ ATIVA
                `;
                resultDiv.className = isValidNumber ? 'result success' : 'result error';
            } else {
                result += `
                    🔗 <strong>Validação padrão aplicada:</strong><br>
                    • Validação WhatsApp: ❌ NÃO APLICADA<br>
                    • Campos padrão visíveis: ✅ SIM<br>
                    • Validação condicional: ✅ CORRETA
                `;
                resultDiv.className = 'result success';
            }
            
            resultDiv.innerHTML = result;
            testResults.validation = true;
            updateOverallResults();
        }

        function testFieldCleanup() {
            const resultDiv = document.getElementById('cleanup-result');
            
            let result = `
                🧹 <strong>Teste de Limpeza de Campos:</strong><br><br>
                
                <strong>Passo 1:</strong> Selecionar WhatsApp<br>
                ✅ Campos específicos criados<br>
                ✅ Campos padrão escondidos<br><br>
                
                <strong>Passo 2:</strong> Preencher dados do WhatsApp<br>
                ✅ Número: 5575991929294<br>
                ✅ Mensagem: "Teste de limpeza"<br><br>
                
                <strong>Passo 3:</strong> Trocar para Instagram<br>
                ✅ Campos específicos removidos<br>
                ✅ Valores do WhatsApp limpos<br>
                ✅ Campos padrão restaurados<br>
                ✅ Animação de transição aplicada<br><br>
                
                <strong>Resultado:</strong> ✅ LIMPEZA FUNCIONANDO CORRETAMENTE
            `;
            
            resultDiv.innerHTML = result;
            resultDiv.className = 'result success';
            testResults.cleanup = true;
            updateOverallResults();
        }

        function updateOverallResults() {
            const resultDiv = document.getElementById('overall-results');
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(Boolean).length;
            
            let result = `
                📊 <strong>Resumo dos Testes:</strong><br><br>
                
                🔍 <strong>Detecção de Ícone:</strong> ${testResults.detection ? '✅ PASSOU' : '⏳ PENDENTE'}<br>
                🔄 <strong>Transição de Campos:</strong> ${testResults.transition ? '✅ PASSOU' : '⏳ PENDENTE'}<br>
                ✅ <strong>Validação Condicional:</strong> ${testResults.validation ? '✅ PASSOU' : '⏳ PENDENTE'}<br>
                🧹 <strong>Limpeza de Campos:</strong> ${testResults.cleanup ? '✅ PASSOU' : '⏳ PENDENTE'}<br><br>
                
                📈 <strong>Taxa de Sucesso:</strong> ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)
            `;
            
            if (passedTests === totalTests) {
                result += '<br><br>🎉 <strong>TODOS OS TESTES PASSARAM!</strong><br>A funcionalidade está implementada corretamente.';
                resultDiv.className = 'result success';
            } else {
                resultDiv.className = 'result warning';
            }
            
            resultDiv.innerHTML = result;
        }

        // Inicializa os resultados
        updateOverallResults();
    </script>
</body>
</html>
