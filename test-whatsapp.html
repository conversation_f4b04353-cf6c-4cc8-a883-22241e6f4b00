<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste WhatsApp - Link Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #333;
        }
        
        .test-button {
            background: #25d366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #128c7e;
        }
        
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: #333;
            border: 1px solid #555;
            border-radius: 5px;
            color: #fff;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste da Funcionalidade WhatsApp</h1>
    
    <div class="test-section">
        <h2>📱 Teste de Geração de URL do WhatsApp</h2>
        <div>
            <label>Número:</label>
            <input type="tel" id="test-number" placeholder="5575991929294" value="5575991929294">
        </div>
        <div>
            <label>Mensagem:</label>
            <textarea id="test-message" placeholder="Olá! Gostaria de agendar um horário...">Olá! Gostaria de agendar um horário no Estúdio730.</textarea>
        </div>
        <button class="test-button" onclick="testWhatsAppUrl()">Gerar URL</button>
        <div class="result" id="url-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🔍 Teste de Parse de URL do WhatsApp</h2>
        <div>
            <label>URL do WhatsApp:</label>
            <input type="text" id="test-url" placeholder="https://wa.me/5575991929294?text=Olá..." value="https://wa.me/5575991929294?text=Olá! Gostaria de agendar um horário no Estúdio730.">
        </div>
        <button class="test-button" onclick="testParseUrl()">Fazer Parse</button>
        <div class="result" id="parse-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Teste de Detecção de Tipo</h2>
        <div>
            <label>Ícone:</label>
            <select id="test-icon">
                <option value="fab fa-whatsapp">WhatsApp</option>
                <option value="fab fa-instagram">Instagram</option>
                <option value="fas fa-globe">Site</option>
                <option value="fas fa-envelope">Email</option>
            </select>
        </div>
        <button class="test-button" onclick="testDetectType()">Detectar Tipo</button>
        <div class="result" id="type-result"></div>
    </div>
    
    <div class="test-section">
        <h2>✅ Teste de Validação</h2>
        <div>
            <label>Número para validar:</label>
            <input type="tel" id="test-validation" placeholder="Digite um número...">
        </div>
        <button class="test-button" onclick="testValidation()">Validar</button>
        <div class="result" id="validation-result"></div>
    </div>

    <script>
        // Simula as funções do ConfigManager para teste
        function generateWhatsAppUrl(number, message) {
            if (!number || !number.trim()) {
                return null;
            }
            
            const cleanNumber = number.trim();
            
            // Valida número
            if (cleanNumber.length < 10 || cleanNumber.length > 15 || !/^\d+$/.test(cleanNumber)) {
                return null;
            }
            
            // Constrói URL
            let url = `https://wa.me/${cleanNumber}`;
            if (message && message.trim()) {
                url += `?text=${encodeURIComponent(message.trim())}`;
            }
            
            return url;
        }
        
        function parseWhatsAppUrl(url) {
            const result = { number: '', message: '' };
            
            if (!url || !url.includes('wa.me')) {
                return result;
            }
            
            try {
                const urlObj = new URL(url);
                
                // Extrai o número do path
                const pathParts = urlObj.pathname.split('/');
                if (pathParts.length > 1) {
                    result.number = pathParts[1];
                }
                
                // Extrai a mensagem dos parâmetros
                const textParam = urlObj.searchParams.get('text');
                if (textParam) {
                    result.message = decodeURIComponent(textParam);
                }
            } catch (error) {
                console.warn('Erro ao fazer parse da URL do WhatsApp:', error);
            }
            
            return result;
        }
        
        function detectLinkType(iconClass) {
            const linkTypeMap = {
                'fab fa-whatsapp': 'whatsapp',
                'fab fa-telegram': 'telegram',
                'fas fa-envelope': 'email',
                'fas fa-phone': 'phone'
            };
            
            return linkTypeMap[iconClass] || 'default';
        }
        
        function validateWhatsAppNumber(number) {
            const value = number.trim();
            return value.length >= 10 && value.length <= 15 && /^\d+$/.test(value);
        }
        
        // Funções de teste
        function testWhatsAppUrl() {
            const number = document.getElementById('test-number').value;
            const message = document.getElementById('test-message').value;
            const url = generateWhatsAppUrl(number, message);
            
            document.getElementById('url-result').innerHTML = url ? 
                `✅ URL gerada: <br><a href="${url}" target="_blank" style="color: #25d366;">${url}</a>` : 
                '❌ Erro: Número inválido';
        }
        
        function testParseUrl() {
            const url = document.getElementById('test-url').value;
            const result = parseWhatsAppUrl(url);
            
            document.getElementById('parse-result').innerHTML = `
                📞 Número: ${result.number || 'Não encontrado'}<br>
                💬 Mensagem: ${result.message || 'Não encontrada'}
            `;
        }
        
        function testDetectType() {
            const icon = document.getElementById('test-icon').value;
            const type = detectLinkType(icon);
            
            document.getElementById('type-result').innerHTML = `
                🎯 Tipo detectado: <strong>${type}</strong><br>
                🎨 Ícone: ${icon}
            `;
        }
        
        function testValidation() {
            const number = document.getElementById('test-validation').value;
            const isValid = validateWhatsAppNumber(number);
            
            document.getElementById('validation-result').innerHTML = isValid ? 
                `✅ Número válido: ${number}` : 
                `❌ Número inválido: ${number}`;
        }
    </script>
</body>
</html>
