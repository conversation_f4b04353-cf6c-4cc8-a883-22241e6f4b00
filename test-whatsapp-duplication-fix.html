<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Correção Duplicação WhatsApp</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .test-container {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #fff;
        }
        
        select, input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .whatsapp-fields {
            background: rgba(37, 211, 102, 0.1);
            border: 2px solid #25d366;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .warning { color: #ffd43b; }
    </style>
</head>
<body>
    <h1>🧪 Teste - Correção Duplicação de Campos WhatsApp</h1>
    
    <div class="test-container">
        <h2>📋 Cenário de Teste</h2>
        <p>Este teste simula o problema de duplicação de campos específicos do WhatsApp:</p>
        <ol>
            <li>Selecionar WhatsApp → Campos aparecem ✅</li>
            <li>Selecionar Instagram → Campos removidos ✅</li>
            <li>Selecionar WhatsApp novamente → <strong>Verificar se NÃO há duplicação</strong></li>
        </ol>
    </div>

    <div class="test-container">
        <h2>🎯 Formulário de Teste</h2>
        
        <div class="form-group">
            <label for="test-icon">Ícone do Serviço</label>
            <select id="test-icon">
                <option value="">Selecione um ícone</option>
                <option value="fab fa-whatsapp">📱 WhatsApp</option>
                <option value="fab fa-instagram">📷 Instagram</option>
                <option value="fab fa-facebook">📘 Facebook</option>
                <option value="fas fa-link">🔗 Link Genérico</option>
            </select>
        </div>

        <div class="form-group">
            <label for="test-url">URL</label>
            <input type="text" id="test-url" placeholder="Digite a URL">
        </div>

        <div id="dynamic-fields-container">
            <!-- Campos dinâmicos aparecerão aqui -->
        </div>

        <div style="margin: 20px 0;">
            <button class="test-button" onclick="runSequentialTest()">🔄 Executar Teste Sequencial</button>
            <button class="test-button" onclick="clearAll()">🧹 Limpar Tudo</button>
            <button class="test-button" onclick="inspectDOM()">🔍 Inspecionar DOM</button>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 Log de Teste</h2>
        <div id="test-log" class="log"></div>
    </div>

    <script>
        let testStep = 0;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function simulateIconChange(iconValue) {
            const select = document.getElementById('test-icon');
            const container = document.getElementById('dynamic-fields-container');
            
            select.value = iconValue;
            log(`🎯 Selecionando ícone: ${iconValue}`);
            
            // Simula a lógica de remoção e criação de campos
            removeSpecificFields();
            
            if (iconValue === 'fab fa-whatsapp') {
                createWhatsAppFields(container);
            } else {
                showDefaultFields();
            }
        }

        function removeSpecificFields() {
            log('🧹 Removendo campos específicos...');
            
            // Remove TODOS os containers do WhatsApp
            const allWhatsappContainers = document.querySelectorAll('[id*="whatsapp-fields"]');
            allWhatsappContainers.forEach(container => {
                container.remove();
                log(`✅ Container WhatsApp removido: ${container.id}`);
            });
            
            // Remove campos individuais
            const specificFields = ['whatsapp-number', 'whatsapp-message'];
            specificFields.forEach(fieldId => {
                const fields = document.querySelectorAll(`[id="${fieldId}"]`);
                fields.forEach(field => {
                    const container = field.closest('.form-group') || field.closest('.whatsapp-fields');
                    if (container) {
                        container.remove();
                        log(`✅ Campo específico removido: ${fieldId}`);
                    }
                });
            });
            
            // Remove containers órfãos
            const orphanContainers = document.querySelectorAll('.whatsapp-fields');
            orphanContainers.forEach(container => {
                container.remove();
                log(`🧹 Container órfão removido`);
            });
        }

        function createWhatsAppFields(container) {
            log('📱 Criando campos específicos do WhatsApp...');
            
            // VERIFICAÇÃO CRÍTICA: Se já existe um container, não cria outro
            const existingContainer = document.getElementById('whatsapp-fields');
            if (existingContainer) {
                log('⚠️ Container WhatsApp já existe - pulando criação', 'warning');
                return;
            }
            
            const whatsappContainer = document.createElement('div');
            whatsappContainer.className = 'whatsapp-fields';
            whatsappContainer.id = 'whatsapp-fields';
            
            whatsappContainer.innerHTML = `
                <h4 style="color: #25d366; margin: 0 0 10px 0;">📱 Campos Específicos do WhatsApp</h4>
                <div class="form-group">
                    <label for="whatsapp-number">Número do WhatsApp *</label>
                    <input type="tel" id="whatsapp-number" placeholder="5575991929294">
                </div>
                <div class="form-group">
                    <label for="whatsapp-message">Mensagem (opcional)</label>
                    <textarea id="whatsapp-message" placeholder="Olá! Gostaria de agendar..." rows="3"></textarea>
                </div>
            `;
            
            container.appendChild(whatsappContainer);
            log('✅ Campos do WhatsApp criados com sucesso', 'success');
        }

        function showDefaultFields() {
            log('🔗 Exibindo campos padrão de URL');
            const urlField = document.getElementById('test-url');
            urlField.style.display = 'block';
        }

        function runSequentialTest() {
            log('🚀 Iniciando teste sequencial...', 'success');
            testStep = 0;
            
            setTimeout(() => {
                log('📍 PASSO 1: Selecionando WhatsApp');
                simulateIconChange('fab fa-whatsapp');
                inspectDOM();
            }, 500);
            
            setTimeout(() => {
                log('📍 PASSO 2: Selecionando Instagram');
                simulateIconChange('fab fa-instagram');
                inspectDOM();
            }, 2000);
            
            setTimeout(() => {
                log('📍 PASSO 3: Selecionando WhatsApp novamente (TESTE CRÍTICO)');
                simulateIconChange('fab fa-whatsapp');
                inspectDOM();
                
                // Verifica duplicação
                const whatsappContainers = document.querySelectorAll('.whatsapp-fields');
                const numberFields = document.querySelectorAll('[id="whatsapp-number"]');
                
                if (whatsappContainers.length > 1 || numberFields.length > 1) {
                    log(`❌ FALHA: Duplicação detectada! Containers: ${whatsappContainers.length}, Campos: ${numberFields.length}`, 'error');
                } else {
                    log(`✅ SUCESSO: Nenhuma duplicação detectada! Containers: ${whatsappContainers.length}, Campos: ${numberFields.length}`, 'success');
                }
            }, 3500);
        }

        function clearAll() {
            document.getElementById('dynamic-fields-container').innerHTML = '';
            document.getElementById('test-icon').value = '';
            document.getElementById('test-log').innerHTML = '';
            log('🧹 Tudo limpo!', 'success');
        }

        function inspectDOM() {
            const whatsappContainers = document.querySelectorAll('.whatsapp-fields');
            const numberFields = document.querySelectorAll('[id="whatsapp-number"]');
            const messageFields = document.querySelectorAll('[id="whatsapp-message"]');
            
            log(`🔍 DOM: ${whatsappContainers.length} containers, ${numberFields.length} campos número, ${messageFields.length} campos mensagem`);
        }

        // Event listener para mudanças manuais
        document.getElementById('test-icon').addEventListener('change', function() {
            simulateIconChange(this.value);
        });

        log('✅ Teste carregado e pronto!', 'success');
    </script>
</body>
</html>
